import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@apollo/ui"
import {
  <PERSON><PERSON>,
  ArrowRight,
  Bell,
  CheckCircle,
  Heart,
  Home,
  Loading,
  Search,
  Star,
  User,
} from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

import { IconGalleryComponent } from "./components/IconGalleryComponent"

/**
 * # Apollo Icons Gallery
 * 
 * The Apollo Icons package provides a comprehensive collection of SVG icons designed specifically 
 * for the Apollo Design System. All icons are optimized for performance, accessibility, and 
 * consistent visual design.
 * 
 * ## Features
 * - **178+ Icons**: Comprehensive collection covering common UI needs
 * - **Consistent Design**: All icons follow the same design principles
 * - **Accessibility**: Built-in ARIA support and semantic markup
 * - **Performance**: Tree-shakable imports and optimized SVGs
 * - **Customizable**: Support for size, color, and styling props
 * - **TypeScript**: Full TypeScript support with proper type definitions
 */
const meta = {
  title: "Icons/Gallery",
  component: IconGalleryComponent,
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "number", min: 12, max: 64, step: 4 },
      description: "Size of the icon in pixels",
      defaultValue: 24,
      table: {
        type: { summary: "string | number" },
        defaultValue: { summary: "24" },
      },
    },
    color: {
      control: { type: "color" },
      description: "Color of the icon (CSS color value)",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "currentColor" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names",
      table: {
        type: { summary: "string" },
      },
    },
    style: {
      control: { type: "object" },
      description: "Inline styles object",
      table: {
        type: { summary: "React.CSSProperties" },
      },
    },
    "aria-label": {
      control: { type: "text" },
      description: "Accessible label for screen readers (use when icon has semantic meaning)",
      table: {
        type: { summary: "string" },
      },
    },
    "aria-hidden": {
      control: { type: "boolean" },
      description: "Hide icon from screen readers (use for decorative icons)",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    role: {
      control: { type: "text" },
      description: "ARIA role (typically 'img' for meaningful icons)",
      table: {
        type: { summary: "string" },
      },
    },
  },
  parameters: {
    layout: "fullscreen",
    docs: {
      page: () => (
        <>
          <Title />
          <Subtitle>
            A comprehensive collection of 178+ icons for the Apollo Design System
          </Subtitle>
          <Description />
          
          <h2 id="installation">Installation</h2>
          <p>
            To use Apollo Icons in your project, you need to install the package and configure your registry:
          </p>
          
          <h3>1. Update .npmrc</h3>
          <Source 
            code={`@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
//gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/:_authToken=<AUTH_TOKEN>`}
            language="text"
          />
          
          <h3>2. Install the Package</h3>
          <Source 
            code={`# Using npm
npm install @design-systems/apollo-icons

# Using yarn  
yarn add @design-systems/apollo-icons

# Using pnpm
pnpm add @design-systems/apollo-icons`}
            language="bash"
          />

          <h2 id="basic-usage">Basic Usage</h2>
          <p>Import individual icons from the package for optimal tree-shaking:</p>
          <Source 
            code={`import { Heart, Search, CheckCircle } from "@design-systems/apollo-icons"

function MyComponent() {
  return (
    <div>
      <Heart size={24} />
      <Search size={20} />
      <CheckCircle size={16} color="green" />
    </div>
  )
}`}
            language="tsx"
          />

          <h2 id="icon-gallery">Icon Gallery</h2>
          <p>
            Browse all available icons below. Click on any icon to copy its import statement to your clipboard.
          </p>
          <Primary />

          <h2 id="props">Props</h2>
          <p>All Apollo Icons support the following props:</p>
          <ArgTypes />

          <h2 id="usage-examples">Usage Examples</h2>
          <Stories />
        </>
      ),
    },
  },
} satisfies Meta<typeof IconGalleryComponent>

export default meta
type Story = StoryObj<typeof meta>

/**
 * The main icon gallery showcasing all available icons with search functionality.
 * Click on any icon to copy its import statement to your clipboard.
 */
export const Gallery: Story = {
  render: () => <IconGalleryComponent />,
  parameters: {
    docs: {
      description: {
        story: "Interactive gallery of all available Apollo icons with search and copy functionality.",
      },
    },
  },
}

/**
 * Basic icon usage examples showing different sizes and styling options.
 */
export const BasicUsage: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", alignItems: "center", flexWrap: "wrap" }}>
      <Heart size={16} />
      <Heart size={24} />
      <Heart size={32} />
      <Heart size={48} style={{ color: "red" }} />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Icons can be sized using the `size` prop and styled with CSS or inline styles.",
      },
      source: {
        code: `<Heart size={16} />
<Heart size={24} />
<Heart size={32} />
<Heart size={48} style={{ color: "red" }} />`,
      },
    },
  },
}

/**
 * Examples of icons commonly used in navigation and UI elements.
 */
export const CommonIcons: Story = {
  render: () => (
    <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(120px, 1fr))", gap: "16px", padding: "16px" }}>
      {[
        { icon: Home, name: "Home" },
        { icon: Search, name: "Search" },
        { icon: User, name: "User" },
        { icon: Heart, name: "Heart" },
        { icon: Star, name: "Star" },
        { icon: CheckCircle, name: "CheckCircle" },
        { icon: Alert, name: "Alert" },
        { icon: ArrowRight, name: "ArrowRight" },
      ].map(({ icon: Icon, name }) => (
        <div key={name} style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: "8px" }}>
          <Icon size={32} />
          <Typography level="caption">{name}</Typography>
        </div>
      ))}
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Most commonly used icons in typical UI applications.",
      },
    },
  },
}

/**
 * Examples showing how to use icons with different color schemes and themes.
 */
export const ColorAndTheming: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "24px", flexWrap: "wrap" }}>
      <div style={{ display: "flex", flexDirection: "column", gap: "8px", alignItems: "center" }}>
        <Typography level="caption">Default</Typography>
        <CheckCircle size={32} />
      </div>
      <div style={{ display: "flex", flexDirection: "column", gap: "8px", alignItems: "center" }}>
        <Typography level="caption">Success</Typography>
        <CheckCircle size={32} style={{ color: "#10b981" }} />
      </div>
      <div style={{ display: "flex", flexDirection: "column", gap: "8px", alignItems: "center" }}>
        <Typography level="caption">Warning</Typography>
        <Alert size={32} style={{ color: "#f59e0b" }} />
      </div>
      <div style={{ display: "flex", flexDirection: "column", gap: "8px", alignItems: "center" }}>
        <Typography level="caption">Error</Typography>
        <Alert size={32} style={{ color: "#ef4444" }} />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Icons inherit the current text color by default and can be styled with CSS color properties.",
      },
      source: {
        code: `<CheckCircle size={32} style={{ color: "#10b981" }} />
<Alert size={32} style={{ color: "#f59e0b" }} />`,
      },
    },
  },
}

/**
 * Examples demonstrating proper accessibility practices with icons.
 */
export const Accessibility: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <CheckCircle size={20} aria-label="Success" role="img" />
        <Typography level="body1">Task completed successfully</Typography>
      </div>
      <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <Alert size={20} aria-label="Warning" role="img" />
        <Typography level="body1">Please review your input</Typography>
      </div>
      <Button style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <Search size={16} aria-hidden="true" />
        Search
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Use `aria-label` and `role='img'` for standalone icons, or `aria-hidden='true'` when icons are decorative.",
      },
      source: {
        code: `// Standalone meaningful icon
<CheckCircle size={20} aria-label="Success" role="img" />

// Decorative icon with text
<Button>
  <Search size={16} aria-hidden="true" />
  Search
</Button>`,
      },
    },
  },
}

/**
 * Examples showing icons used in interactive elements like buttons and links.
 */
export const InteractiveElements: Story = {
  render: () => (
    <div style={{ display: "flex", gap: "16px", flexWrap: "wrap" }}>
      <Button variant="filled" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <ArrowRight size={16} />
        Continue
      </Button>
      <Button variant="outline" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <Heart size={16} />
        Like
      </Button>
      <Button variant="text" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <Star size={16} />
        Favorite
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Icons work seamlessly with interactive elements like buttons and maintain proper spacing.",
      },
    },
  },
}

/**
 * Performance optimization examples showing tree-shaking and dynamic imports.
 */
export const PerformanceOptimization: Story = {
  render: () => (
    <div style={{ padding: "16px", backgroundColor: "#f8f9fa", borderRadius: "8px" }}>
      <Typography level="h4" style={{ marginBottom: "16px" }}>
        Import Strategies
      </Typography>
      <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
        <div>
          <Typography level="body2" style={{ fontWeight: "bold", color: "#10b981" }}>
            ✅ Recommended: Named imports
          </Typography>
          <Typography level="caption" style={{ fontFamily: "monospace", backgroundColor: "#e5e7eb", padding: "4px 8px", borderRadius: "4px" }}>
            import {`{ Heart, Search }`} from "@design-systems/apollo-icons"
          </Typography>
        </div>
        <div>
          <Typography level="body2" style={{ fontWeight: "bold", color: "#ef4444" }}>
            ❌ Avoid: Barrel imports
          </Typography>
          <Typography level="caption" style={{ fontFamily: "monospace", backgroundColor: "#e5e7eb", padding: "4px 8px", borderRadius: "4px" }}>
            import * as Icons from "@design-systems/apollo-icons"
          </Typography>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Use named imports for optimal tree-shaking and bundle size optimization.",
      },
    },
  },
}

/**
 * Comprehensive guide to icon best practices covering sizing, accessibility, performance, and styling.
 */
export const BestPractices: Story = {
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: "32px", maxWidth: "800px" }}>
      {/* Sizing Guidelines */}
      <div>
        <Typography level="h3" style={{ marginBottom: "16px" }}>
          Sizing Guidelines
        </Typography>
        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <div style={{ display: "flex", alignItems: "center", gap: "16px", padding: "16px", backgroundColor: "#f8f9fa", borderRadius: "8px" }}>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Search size={16} />
              <Typography level="caption">16px - Small UI elements</Typography>
            </div>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Search size={20} />
              <Typography level="caption">20px - Form inputs</Typography>
            </div>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Search size={24} />
              <Typography level="caption">24px - Default size</Typography>
            </div>
            <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
              <Search size={32} />
              <Typography level="caption">32px - Prominent actions</Typography>
            </div>
          </div>
          <Typography level="body2" style={{ color: "#6b7280" }}>
            Use consistent sizing throughout your application. Stick to multiples of 4 for better visual alignment.
          </Typography>
        </div>
      </div>

      {/* Accessibility Guidelines */}
      <div>
        <Typography level="h3" style={{ marginBottom: "16px" }}>
          Accessibility Best Practices
        </Typography>
        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <div style={{ padding: "16px", backgroundColor: "#f0f9ff", borderRadius: "8px", border: "1px solid #0ea5e9" }}>
            <Typography level="body2" style={{ fontWeight: "bold", marginBottom: "8px" }}>
              ✅ Do: Meaningful Icons
            </Typography>
            <div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}>
              <CheckCircle size={20} aria-label="Task completed successfully" />
              <Typography level="body2">Use aria-label for standalone meaningful icons</Typography>
            </div>
            <Typography level="caption" style={{ fontFamily: "monospace", backgroundColor: "#e0f2fe", padding: "4px 8px", borderRadius: "4px" }}>
              {`<CheckCircle size={20} aria-label="Task completed successfully" />`}
            </Typography>
          </div>

          <div style={{ padding: "16px", backgroundColor: "#f0f9ff", borderRadius: "8px", border: "1px solid #0ea5e9" }}>
            <Typography level="body2" style={{ fontWeight: "bold", marginBottom: "8px" }}>
              ✅ Do: Decorative Icons
            </Typography>
            <Button style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}>
              <Search size={16} aria-hidden="true" />
              Search Products
            </Button>
            <Typography level="caption" style={{ fontFamily: "monospace", backgroundColor: "#e0f2fe", padding: "4px 8px", borderRadius: "4px" }}>
              {`<Search size={16} aria-hidden="true" />`}
            </Typography>
          </div>
        </div>
      </div>

      {/* Performance Guidelines */}
      <div>
        <Typography level="h3" style={{ marginBottom: "16px" }}>
          Performance Optimization
        </Typography>
        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <div style={{ padding: "16px", backgroundColor: "#f0fdf4", borderRadius: "8px", border: "1px solid #22c55e" }}>
            <Typography level="body2" style={{ fontWeight: "bold", color: "#16a34a", marginBottom: "8px" }}>
              ✅ Recommended: Tree-shakable imports
            </Typography>
            <Typography level="caption" style={{ fontFamily: "monospace", backgroundColor: "#dcfce7", padding: "4px 8px", borderRadius: "4px", display: "block", marginBottom: "8px" }}>
              {`import { Heart, Search, User } from "@design-systems/apollo-icons"`}
            </Typography>
            <Typography level="body2" style={{ color: "#16a34a" }}>
              Only imports the icons you actually use, reducing bundle size.
            </Typography>
          </div>

          <div style={{ padding: "16px", backgroundColor: "#fef2f2", borderRadius: "8px", border: "1px solid #ef4444" }}>
            <Typography level="body2" style={{ fontWeight: "bold", color: "#dc2626", marginBottom: "8px" }}>
              ❌ Avoid: Barrel imports
            </Typography>
            <Typography level="caption" style={{ fontFamily: "monospace", backgroundColor: "#fee2e2", padding: "4px 8px", borderRadius: "4px", display: "block", marginBottom: "8px" }}>
              {`import * as Icons from "@design-systems/apollo-icons"`}
            </Typography>
            <Typography level="body2" style={{ color: "#dc2626" }}>
              Imports all icons, significantly increasing bundle size.
            </Typography>
          </div>
        </div>
      </div>

      {/* Styling Guidelines */}
      <div>
        <Typography level="h3" style={{ marginBottom: "16px" }}>
          Styling Best Practices
        </Typography>
        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <div style={{ padding: "16px", backgroundColor: "#f8f9fa", borderRadius: "8px" }}>
            <Typography level="body2" style={{ fontWeight: "bold", marginBottom: "12px" }}>
              Color Inheritance
            </Typography>
            <div style={{ display: "flex", alignItems: "center", gap: "16px", marginBottom: "8px" }}>
              <div style={{ color: "#6b7280" }}>
                <Heart size={24} />
              </div>
              <div style={{ color: "#ef4444" }}>
                <Heart size={24} />
              </div>
              <div style={{ color: "#10b981" }}>
                <Heart size={24} />
              </div>
            </div>
            <Typography level="body2" style={{ color: "#6b7280" }}>
              Icons inherit the current text color by default. Use CSS color properties or inline styles to customize.
            </Typography>
          </div>

          <div style={{ padding: "16px", backgroundColor: "#f8f9fa", borderRadius: "8px" }}>
            <Typography level="body2" style={{ fontWeight: "bold", marginBottom: "12px" }}>
              Consistent Spacing
            </Typography>
            <div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}>
              <Button style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Heart size={16} />
                Like
              </Button>
              <Button style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <Star size={16} />
                Favorite
              </Button>
            </div>
            <Typography level="body2" style={{ color: "#6b7280" }}>
              Maintain consistent spacing between icons and text (typically 8px gap).
            </Typography>
          </div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: "Comprehensive guidelines for using Apollo Icons effectively in your applications.",
      },
    },
  },
}

// Component for advanced usage examples
const AdvancedUsageComponent = () => {
  const [isLiked, setIsLiked] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleAsyncAction = async () => {
    setLoading(true)
    await new Promise(resolve => setTimeout(resolve, 2000))
    setLoading(false)
  }

  return (

    return (
      <div style={{ display: "flex", flexDirection: "column", gap: "32px", maxWidth: "600px" }}>
        {/* Dynamic Icon Selection */}
        <div>
          <Typography level="h4" style={{ marginBottom: "16px" }}>
            Dynamic Icon Selection
          </Typography>
          <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
            <Button
              onClick={() => setIsLiked(!isLiked)}
              style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                color: isLiked ? "#ef4444" : "#6b7280",
                border: `1px solid ${isLiked ? "#ef4444" : "#d1d5db"}`,
                backgroundColor: isLiked ? "#fef2f2" : "transparent"
              }}
            >
              <Heart size={16} style={{ fill: isLiked ? "currentColor" : "none" }} />
              {isLiked ? "Liked" : "Like"}
            </Button>
            <Typography level="caption" style={{ color: "#6b7280" }}>
              Click to toggle state
            </Typography>
          </div>
        </div>

        {/* Loading States */}
        <div>
          <Typography level="h4" style={{ marginBottom: "16px" }}>
            Loading States
          </Typography>
          <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
            <Button
              onClick={handleAsyncAction}
              disabled={loading}
              style={{ display: "flex", alignItems: "center", gap: "8px" }}
            >
              {loading ? (
                <>
                  <div style={{ animation: "spin 1s linear infinite" }}>
                    <Loading size={16} />
                  </div>
                  Processing...
                </>
              ) : (
                <>
                  <CheckCircle size={16} />
                  Start Process
                </>
              )}
            </Button>
            <style>
              {`
                @keyframes spin {
                  from { transform: rotate(0deg); }
                  to { transform: rotate(360deg); }
                }
              `}
            </style>
          </div>
        </div>

        {/* Icon with Custom Styling */}
        <div>
          <Typography level="h4" style={{ marginBottom: "16px" }}>
            Custom Styling Examples
          </Typography>
          <div style={{ display: "flex", gap: "24px", alignItems: "center", flexWrap: "wrap" }}>
            <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: "8px" }}>
              <div style={{
                padding: "12px",
                borderRadius: "50%",
                backgroundColor: "#dbeafe",
                border: "2px solid #3b82f6"
              }}>
                <User size={24} style={{ color: "#3b82f6" }} />
              </div>
              <Typography level="caption">Circular Background</Typography>
            </div>

            <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: "8px" }}>
              <div style={{
                padding: "8px",
                borderRadius: "6px",
                backgroundColor: "#f3e8ff",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
              }}>
                <Star size={24} style={{ color: "#8b5cf6" }} />
              </div>
              <Typography level="caption">Card Style</Typography>
            </div>

            <div style={{ display: "flex", flexDirection: "column", alignItems: "center", gap: "8px" }}>
              <div style={{ position: "relative" }}>
                <Bell size={24} style={{ color: "#6b7280" }} />
                <div style={{
                  position: "absolute",
                  top: "-4px",
                  right: "-4px",
                  width: "12px",
                  height: "12px",
                  borderRadius: "50%",
                  backgroundColor: "#ef4444",
                  border: "2px solid white"
                }} />
              </div>
              <Typography level="caption">With Badge</Typography>
            </div>
          </div>
        </div>

        {/* Responsive Sizing */}
        <div>
          <Typography level="h4" style={{ marginBottom: "16px" }}>
            Responsive Icon Sizing
          </Typography>
          <div style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(100px, 1fr))",
            gap: "16px",
            padding: "16px",
            backgroundColor: "#f8f9fa",
            borderRadius: "8px"
          }}>
            <div style={{ textAlign: "center" }}>
              <Home size="clamp(16px, 4vw, 32px)" />
              <Typography level="caption" style={{ display: "block", marginTop: "8px" }}>
                Responsive
              </Typography>
            </div>
          </div>
          <Typography level="body2" style={{ color: "#6b7280", marginTop: "8px" }}>
            Use CSS clamp() for responsive icon sizing that adapts to viewport width.
          </Typography>
        </div>
      </div>
    )
  )
}

/**
 * Advanced usage patterns including dynamic icons, conditional rendering, and custom styling.
 */
export const AdvancedUsage: Story = {
  render: () => <AdvancedUsageComponent />,
  parameters: {
    docs: {
      description: {
        story: "Advanced patterns for dynamic icons, loading states, custom styling, and responsive design.",
      },
      source: {
        code: `// Dynamic icon state
const [isLiked, setIsLiked] = useState(false)

<Button onClick={() => setIsLiked(!isLiked)}>
  <Heart size={16} style={{ fill: isLiked ? "currentColor" : "none" }} />
  {isLiked ? "Liked" : "Like"}
</Button>

// Loading state with animation
{loading ? (
  <div style={{ animation: "spin 1s linear infinite" }}>
    <Loading size={16} />
  </div>
) : (
  <CheckCircle size={16} />
)}

// Custom styling with background
<div style={{
  padding: "12px",
  borderRadius: "50%",
  backgroundColor: "#dbeafe"
}}>
  <User size={24} style={{ color: "#3b82f6" }} />
</div>

// Responsive sizing
<Home size="clamp(16px, 4vw, 32px)" />`,
      },
    },
  },
}
