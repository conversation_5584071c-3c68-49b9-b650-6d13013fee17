# Apollo Icons Gallery - Storybook Documentation

This directory contains the comprehensive Storybook documentation for the `@design-systems/apollo-icons` package.

## Files Structure

```
icons/
├── IconGallery.stories.tsx     # Main Storybook documentation
├── components/
│   ├── IconGalleryComponent.tsx # Enhanced icon gallery component
│   └── index.ts                # Component exports
└── README.md                   # This file
```

## Features

### 📚 Comprehensive Documentation
- **Installation Guide**: Step-by-step setup instructions
- **Usage Examples**: Multiple code examples for different scenarios
- **Best Practices**: Guidelines for accessibility, performance, and styling
- **Props Documentation**: Complete API reference with controls

### 🎨 Interactive Gallery
- **178+ Icons**: Complete collection with search functionality
- **Categorization**: Icons organized by usage type (Navigation, Actions, etc.)
- **Copy to Clipboard**: Click any icon to copy its import statement
- **Responsive Design**: Works on all screen sizes

### ♿ Accessibility Features
- **Keyboard Navigation**: Full keyboard support for gallery interaction
- **Screen Reader Support**: Proper ARIA labels and semantic markup
- **Focus Management**: Clear focus indicators and logical tab order

### 🚀 Performance Optimized
- **Tree-shakable Imports**: Examples show optimal import patterns
- **Bundle Size Guidance**: Clear do's and don'ts for performance

## Stories Included

1. **Gallery** - Main interactive icon gallery
2. **Basic Usage** - Simple icon usage examples
3. **Common Icons** - Most frequently used icons
4. **Color and Theming** - Styling and color examples
5. **Accessibility** - Proper accessibility implementation
6. **Interactive Elements** - Icons in buttons and UI components
7. **Performance Optimization** - Import strategies and best practices
8. **Best Practices** - Comprehensive usage guidelines
9. **Advanced Usage** - Dynamic icons, loading states, and custom styling

## Usage in Storybook

The documentation is available in Storybook under:
```
Icons > Gallery
```

## Development

To modify the gallery:

1. **IconGalleryComponent.tsx** - Main gallery component with search and categorization
2. **IconGallery.stories.tsx** - Storybook stories and documentation

## Icon Categories

Icons are automatically categorized into:
- Navigation (arrows, carets, directional)
- Actions (check, close, edit, delete)
- Communication (mail, message, phone)
- Files & Documents (file types, folders)
- User & Profile (user management, auth)
- Status & Feedback (alerts, loading, status)
- Commerce & Shopping (cart, payment, products)
- Charts & Analytics (data visualization)
- Media & Content (images, video, audio)
- Tools & Settings (configuration, utilities)
- Time & Calendar (dates, scheduling)
- Miscellaneous (other useful icons)

## Best Practices Covered

### Accessibility
- Use `aria-label` for standalone meaningful icons
- Use `aria-hidden="true"` for decorative icons
- Proper keyboard navigation support

### Performance
- Named imports for tree-shaking: `import { Heart } from "@design-systems/apollo-icons"`
- Avoid barrel imports: `import * as Icons from "@design-systems/apollo-icons"`

### Styling
- Icons inherit text color by default
- Use consistent sizing (multiples of 4px)
- Maintain proper spacing with text (8px gap recommended)

### Responsive Design
- Use CSS `clamp()` for responsive icon sizing
- Consider different screen sizes and touch targets

## Contributing

When adding new icons or updating documentation:

1. Update icon categories in `IconGalleryComponent.tsx` if needed
2. Add new usage examples to the stories
3. Update best practices documentation
4. Test accessibility with screen readers
5. Verify performance impact of changes
